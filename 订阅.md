# TDengine WebSocket 订阅操作详细指南

## 概述

TDengine 的订阅功能基于消息队列（TMQ）机制，类似于 Apache Kafka，提供了高效的流式数据消费能力。通过订阅功能，应用程序可以实时接收数据库中的数据变化，实现数据的实时处理和分析。

## 核心概念

### 1. Topic（主题）
- **定义**: Topic 是数据流的逻辑分组，类似于 Kafka 的 Topic
- **作用**: 将相关的数据流组织在一起，便于消费者订阅
- **创建**: 通过 SQL 语句创建，可以基于数据库、超级表或普通表

### 2. Consumer（消费者）
- **定义**: 订阅 Topic 并消费数据的客户端
- **特点**: 支持消费者组、自动提交、手动提交等功能
- **生命周期**: 创建 → 订阅 → 消费 → 提交 → 取消订阅 → 关闭

### 3. Consumer Group（消费者组）
- **定义**: 多个消费者组成的逻辑组，共同消费一个或多个 Topic
- **作用**: 实现负载均衡和故障转移
- **特点**: 同一组内的消费者不会重复消费同一条消息

### 4. Offset（偏移量）
- **定义**: 消费者在 Topic 中的消费位置标记
- **管理**: 支持自动提交和手动提交
- **重置**: 支持从最早、最新或指定位置开始消费

## 主要组件

### Consumer 类
```python
class Consumer:
    def __init__(conf: dict, dsn: str = None)  # 创建消费者
    def subscribe(topics: list)                # 订阅主题
    def poll(timeout: float) -> Message        # 拉取消息
    def commit(message: Message)               # 提交消息
    def commit_offset(topic, vg_id, offset)    # 提交指定偏移量
    def unsubscribe()                          # 取消订阅
    def close()                                # 关闭消费者
    def assignment() -> list                   # 获取分配信息
    def seek(topic, vg_id, offset)             # 定位到指定偏移量
    def committed(topic, vg_id) -> int         # 获取已提交偏移量
    def position(topic, vg_id) -> int          # 获取当前位置
```

### Message 类
```python
class Message:
    def vgroup() -> int        # 获取虚拟组ID
    def topic() -> str         # 获取主题名称
    def database() -> str      # 获取数据库名称
    def __iter__()             # 迭代消息块
```

### MessageBlock 类
```python
class MessageBlock:
    def nrows() -> int         # 获取行数
    def ncols() -> int         # 获取列数
    def fields() -> list       # 获取字段信息
    def fetchall() -> list     # 获取所有数据
    def __iter__()             # 迭代行数据
```

## 配置参数

### 必需参数
```python
conf = {
    "group.id": "consumer_group_1",  # 消费者组ID（必需）
}
```

### 连接参数
```python
conf = {
    "td.connect.websocket.scheme": "ws",     # WebSocket协议
    "td.connect.ip": "localhost",            # 服务器地址
    "td.connect.port": "6041",               # 端口号
    "td.connect.user": "root",               # 用户名
    "td.connect.pass": "taosdata",           # 密码
    "td.connect.token": "your_token",        # 云服务token
}
```

### 消费行为参数
```python
conf = {
    "auto.offset.reset": "earliest",         # 偏移量重置策略
    "enable.auto.commit": "true",            # 是否自动提交
    "auto.commit.interval.ms": "5000",       # 自动提交间隔
    "fetch.max.wait.ms": "3000",             # 最大等待时间
    "min.poll.rows": "100",                  # 最小拉取行数
    "experimental.snapshot.enable": "false", # 是否启用快照
}
```

### 偏移量重置策略
- `"earliest"`: 从最早的消息开始消费
- `"latest"`: 从最新的消息开始消费
- `"none"`: 如果没有已提交的偏移量则抛出异常

## 基本使用流程

### 1. 创建 Topic
```sql
-- 基于数据库创建Topic
CREATE TOPIC topic_db_data AS DATABASE test_db;

-- 基于超级表创建Topic
CREATE TOPIC topic_stb_data AS STABLE test_db.meters;

-- 基于普通表创建Topic
CREATE TOPIC topic_tb_data AS TABLE test_db.d1001;

-- 基于查询创建Topic（过滤条件）
CREATE TOPIC topic_filtered AS SELECT * FROM test_db.meters WHERE voltage > 200;
```

### 2. 创建消费者
```python
import taosws

# 基本配置
conf = {
    "td.connect.websocket.scheme": "ws",
    "group.id": "my_consumer_group",
    "auto.offset.reset": "earliest",
}

# 创建消费者
consumer = taosws.Consumer(conf)
```

### 3. 订阅主题
```python
# 订阅单个主题
consumer.subscribe(["topic_db_data"])

# 订阅多个主题
consumer.subscribe(["topic_db_data", "topic_stb_data", "topic_tb_data"])
```

### 4. 消费消息
```python
try:
    while True:
        # 拉取消息（超时1秒）
        message = consumer.poll(timeout=1.0)
        
        if message:
            # 获取消息元信息
            vgroup_id = message.vgroup()
            topic_name = message.topic()
            database_name = message.database()
            
            print(f"收到消息 - Topic: {topic_name}, VGroup: {vgroup_id}, DB: {database_name}")
            
            # 处理消息中的数据块
            for block in message:
                print(f"数据块 - 行数: {block.nrows()}, 列数: {block.ncols()}")
                
                # 逐行处理
                for row in block:
                    print(f"数据行: {row}")
                
                # 或批量获取所有数据
                all_data = block.fetchall()
                print(f"所有数据: {all_data}")
            
            # 提交消息（确认消费）
            consumer.commit(message)
        else:
            print("没有新消息")
            break
            
except Exception as e:
    print(f"消费过程中出错: {e}")
finally:
    # 清理资源
    consumer.unsubscribe()
    consumer.close()
```

## 高级功能

### 1. 手动偏移量管理
```python
# 禁用自动提交
conf = {
    "group.id": "manual_commit_group",
    "enable.auto.commit": "false",
}
consumer = taosws.Consumer(conf)

# 手动提交特定偏移量
consumer.commit_offset(topic="my_topic", vg_id=1, offset=100)

# 查询已提交的偏移量
committed_offset = consumer.committed(topic="my_topic", vg_id=1)
print(f"已提交偏移量: {committed_offset}")

# 查询当前位置
current_position = consumer.position(topic="my_topic", vg_id=1)
print(f"当前位置: {current_position}")

# 定位到指定偏移量
consumer.seek(topic="my_topic", vg_id=1, offset=50)
```

### 2. 分配信息查询
```python
# 获取消费者分配信息
assignments = consumer.assignment()

if assignments:
    for topic_assignment in assignments:
        topic = topic_assignment.topic()
        print(f"主题: {topic}")
        
        for assignment in topic_assignment.assignments():
            vg_id = assignment.vg_id()
            offset = assignment.offset()
            begin = assignment.begin()
            end = assignment.end()
            
            print(f"  VGroup: {vg_id}")
            print(f"  当前偏移量: {offset}")
            print(f"  起始偏移量: {begin}")
            print(f"  结束偏移量: {end}")
```

### 3. 主题列表查询
```python
# 获取可用主题列表
topics = consumer.list_topics()
print(f"可用主题: {topics}")
```

## 实际应用场景

### 1. 实时数据监控
```python
def real_time_monitor():
    """实时监控数据变化"""
    conf = {
        "td.connect.websocket.scheme": "ws",
        "group.id": "monitor_group",
        "auto.offset.reset": "latest",  # 只消费新数据
    }
    
    consumer = taosws.Consumer(conf)
    consumer.subscribe(["sensor_data_topic"])
    
    try:
        while True:
            message = consumer.poll(timeout=5.0)
            if message:
                for block in message:
                    for row in block:
                        # 检查异常数据
                        if row[2] > 100:  # 假设第3列是温度
                            print(f"警告: 温度异常 {row}")
                            # 发送告警
                            send_alert(row)
                
                consumer.commit(message)
    except KeyboardInterrupt:
        print("监控停止")
    finally:
        consumer.unsubscribe()
        consumer.close()
```

### 2. 数据ETL处理
```python
def data_etl_processor():
    """数据ETL处理"""
    conf = {
        "td.connect.websocket.scheme": "ws",
        "group.id": "etl_group",
        "auto.offset.reset": "earliest",
        "enable.auto.commit": "false",  # 手动提交确保数据完整性
    }
    
    consumer = taosws.Consumer(conf)
    consumer.subscribe(["raw_data_topic"])
    
    try:
        while True:
            message = consumer.poll(timeout=10.0)
            if message:
                processed_data = []
                
                for block in message:
                    for row in block:
                        # 数据转换和清洗
                        cleaned_row = clean_data(row)
                        transformed_row = transform_data(cleaned_row)
                        processed_data.append(transformed_row)
                
                # 批量写入目标系统
                if processed_data:
                    write_to_target_system(processed_data)
                    consumer.commit(message)  # 确认处理完成
                    print(f"处理了 {len(processed_data)} 条记录")
            else:
                break
    except Exception as e:
        print(f"ETL处理错误: {e}")
    finally:
        consumer.unsubscribe()
        consumer.close()

def clean_data(row):
    """数据清洗函数"""
    # 实现数据清洗逻辑
    return row

def transform_data(row):
    """数据转换函数"""
    # 实现数据转换逻辑
    return row

def write_to_target_system(data):
    """写入目标系统"""
    # 实现数据写入逻辑
    pass
```

### 3. 多消费者负载均衡
```python
import threading
import time

def create_consumer_worker(worker_id, group_id, topics):
    """创建消费者工作线程"""
    conf = {
        "td.connect.websocket.scheme": "ws",
        "group.id": group_id,
        "client.id": f"worker_{worker_id}",
        "auto.offset.reset": "earliest",
    }

    consumer = taosws.Consumer(conf)
    consumer.subscribe(topics)

    try:
        print(f"Worker {worker_id} 开始消费")
        while True:
            message = consumer.poll(timeout=3.0)
            if message:
                vgroup_id = message.vgroup()
                print(f"Worker {worker_id} 处理 VGroup {vgroup_id}")

                for block in message:
                    # 模拟数据处理
                    time.sleep(0.1)
                    print(f"Worker {worker_id} 处理了 {block.nrows()} 行数据")

                consumer.commit(message)
            else:
                print(f"Worker {worker_id} 没有新消息")
                break
    except Exception as e:
        print(f"Worker {worker_id} 错误: {e}")
    finally:
        consumer.unsubscribe()
        consumer.close()
        print(f"Worker {worker_id} 已关闭")

def multi_consumer_example():
    """多消费者负载均衡示例"""
    group_id = "load_balance_group"
    topics = ["high_volume_topic"]
    worker_count = 3

    # 创建多个消费者线程
    threads = []
    for i in range(worker_count):
        thread = threading.Thread(
            target=create_consumer_worker,
            args=(i, group_id, topics)
        )
        threads.append(thread)
        thread.start()

    # 等待所有线程完成
    for thread in threads:
        thread.join()

    print("所有消费者已完成")
```

### 4. 云服务订阅
```python
def cloud_consumer_example():
    """TDengine 云服务订阅示例"""
    conf = {
        "protocol": "taosws",
        "td.connect.ip": "your-instance.cloud.tdengine.com",
        "td.connect.token": "your_cloud_token",
        "group.id": "cloud_consumer_group",
        "client.id": "cloud_client_001",
        "auto.offset.reset": "earliest",
        "enable.auto.commit": "true",
        "auto.commit.interval.ms": "5000",
    }

    consumer = taosws.Consumer(conf)

    try:
        # 订阅云端主题
        consumer.subscribe(["cloud_sensor_data"])
        print("已连接到云服务并订阅主题")

        message_count = 0
        while message_count < 100:  # 处理100条消息后退出
            message = consumer.poll(timeout=10.0)
            if message:
                message_count += 1
                topic = message.topic()
                database = message.database()

                print(f"消息 {message_count}: Topic={topic}, DB={database}")

                for block in message:
                    data = block.fetchall()
                    print(f"  数据块: {len(data)} 行")

                    # 处理云端数据
                    process_cloud_data(data)

                # 云服务建议手动提交以确保数据完整性
                consumer.commit(message)
            else:
                print("等待新消息...")

    except Exception as e:
        print(f"云服务消费错误: {e}")
    finally:
        consumer.unsubscribe()
        consumer.close()

def process_cloud_data(data):
    """处理云端数据"""
    for row in data:
        # 实现具体的业务逻辑
        pass

def send_alert(data):
    """发送告警"""
    # 实现告警逻辑
    pass
```

## 错误处理和重试机制

### 1. 连接错误处理
```python
import time
from taosws import ConsumerException

def robust_consumer():
    """具有错误处理和重试机制的消费者"""
    conf = {
        "td.connect.websocket.scheme": "ws",
        "group.id": "robust_group",
        "auto.offset.reset": "earliest",
    }

    max_retries = 3
    retry_delay = 5  # 秒

    for attempt in range(max_retries):
        try:
            consumer = taosws.Consumer(conf)
            consumer.subscribe(["my_topic"])

            print(f"连接成功 (尝试 {attempt + 1})")

            # 消费循环
            consecutive_empty = 0
            max_empty = 10  # 连续空消息次数限制

            while consecutive_empty < max_empty:
                try:
                    message = consumer.poll(timeout=5.0)
                    if message:
                        consecutive_empty = 0
                        process_message_safely(message, consumer)
                    else:
                        consecutive_empty += 1
                        print(f"空消息计数: {consecutive_empty}")

                except ConsumerException as e:
                    print(f"消费错误: {e}")
                    if "connection" in str(e).lower():
                        # 连接错误，需要重新连接
                        break
                    else:
                        # 其他错误，继续尝试
                        time.sleep(1)
                        continue

            # 正常退出
            consumer.unsubscribe()
            consumer.close()
            break

        except Exception as e:
            print(f"连接失败 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                print(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                print("达到最大重试次数，退出")
                raise

def process_message_safely(message, consumer):
    """安全处理消息"""
    try:
        # 处理消息数据
        for block in message:
            for row in block:
                # 业务逻辑处理
                process_row(row)

        # 成功处理后提交
        consumer.commit(message)

    except Exception as e:
        print(f"消息处理错误: {e}")
        # 根据错误类型决定是否重试或跳过
        if is_retryable_error(e):
            raise  # 重新抛出，触发重试
        else:
            # 跳过这条消息，避免阻塞
            consumer.commit(message)
            print("跳过错误消息")

def process_row(row):
    """处理单行数据"""
    # 实现具体的业务逻辑
    pass

def is_retryable_error(error):
    """判断错误是否可重试"""
    retryable_keywords = ["timeout", "network", "connection"]
    error_str = str(error).lower()
    return any(keyword in error_str for keyword in retryable_keywords)
```

### 2. 消息处理失败重试
```python
import json
from datetime import datetime

def message_processor_with_retry():
    """带重试机制的消息处理器"""
    conf = {
        "td.connect.websocket.scheme": "ws",
        "group.id": "retry_group",
        "enable.auto.commit": "false",  # 手动提交控制
    }

    consumer = taosws.Consumer(conf)
    consumer.subscribe(["data_topic"])

    failed_messages = []  # 失败消息队列

    try:
        while True:
            message = consumer.poll(timeout=5.0)
            if message:
                success = process_message_with_retry(message, max_retries=3)
                if success:
                    consumer.commit(message)
                else:
                    # 记录失败消息
                    failed_messages.append({
                        'topic': message.topic(),
                        'vgroup': message.vgroup(),
                        'timestamp': datetime.now().isoformat(),
                        'data': extract_message_data(message)
                    })

                    # 仍然提交以避免重复消费
                    consumer.commit(message)
            else:
                # 处理失败消息队列
                if failed_messages:
                    print(f"处理 {len(failed_messages)} 条失败消息")
                    retry_failed_messages(failed_messages)
                    failed_messages.clear()
                break

    finally:
        # 保存未处理的失败消息
        if failed_messages:
            save_failed_messages(failed_messages)

        consumer.unsubscribe()
        consumer.close()

def process_message_with_retry(message, max_retries=3):
    """重试处理消息"""
    for attempt in range(max_retries):
        try:
            for block in message:
                for row in block:
                    # 模拟可能失败的处理
                    if not process_row_safely(row):
                        raise Exception("行处理失败")
            return True

        except Exception as e:
            print(f"处理失败 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
            else:
                print("达到最大重试次数")
                return False

def extract_message_data(message):
    """提取消息数据"""
    data = []
    for block in message:
        data.extend(block.fetchall())
    return data

def save_failed_messages(failed_messages):
    """保存失败消息到文件"""
    filename = f"failed_messages_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(filename, 'w') as f:
        json.dump(failed_messages, f, indent=2)
    print(f"失败消息已保存到 {filename}")

def retry_failed_messages(failed_messages):
    """重试失败消息"""
    for msg in failed_messages:
        try:
            # 重新处理失败的数据
            for row in msg['data']:
                process_row_safely(row)
            print(f"重试成功: {msg['topic']}")
        except Exception as e:
            print(f"重试仍然失败: {e}")

def process_row_safely(row):
    """安全处理行数据"""
    try:
        # 实现具体的业务逻辑
        return True
    except Exception:
        return False
```

## 性能优化和最佳实践

### 1. 批量处理优化
```python
def batch_processing_consumer():
    """批量处理优化的消费者"""
    conf = {
        "td.connect.websocket.scheme": "ws",
        "group.id": "batch_group",
        "fetch.max.wait.ms": "1000",      # 减少等待时间
        "min.poll.rows": "1000",          # 增加最小拉取行数
        "enable.auto.commit": "false",    # 手动控制提交
    }

    consumer = taosws.Consumer(conf)
    consumer.subscribe(["high_volume_topic"])

    batch_size = 5000
    batch_data = []
    last_commit_time = time.time()
    commit_interval = 30  # 30秒提交一次

    try:
        while True:
            message = consumer.poll(timeout=2.0)
            if message:
                # 收集数据到批次中
                for block in message:
                    batch_data.extend(block.fetchall())

                # 检查是否达到批次大小或时间间隔
                current_time = time.time()
                if (len(batch_data) >= batch_size or
                    current_time - last_commit_time >= commit_interval):

                    # 批量处理数据
                    if batch_data:
                        process_batch(batch_data)
                        batch_data.clear()

                    # 提交偏移量
                    consumer.commit(message)
                    last_commit_time = current_time
                    print(f"批量处理完成，提交偏移量")
            else:
                # 处理剩余数据
                if batch_data:
                    process_batch(batch_data)
                    batch_data.clear()
                break

    finally:
        consumer.unsubscribe()
        consumer.close()

def process_batch(data_batch):
    """批量处理数据"""
    print(f"批量处理 {len(data_batch)} 条记录")
    # 实现批量处理逻辑，如批量写入数据库
    batch_insert_to_db(data_batch)

def batch_insert_to_db(data):
    """批量插入数据库"""
    # 实现批量插入逻辑
    pass
```

### 2. 内存管理优化
```python
import gc
from collections import deque

def memory_efficient_consumer():
    """内存高效的消费者"""
    conf = {
        "td.connect.websocket.scheme": "ws",
        "group.id": "memory_efficient_group",
        "auto.offset.reset": "earliest",
    }

    consumer = taosws.Consumer(conf)
    consumer.subscribe(["large_data_topic"])

    # 使用循环缓冲区限制内存使用
    max_buffer_size = 10000
    data_buffer = deque(maxlen=max_buffer_size)

    processed_count = 0
    gc_interval = 1000  # 每处理1000条消息执行一次垃圾回收

    try:
        while True:
            message = consumer.poll(timeout=5.0)
            if message:
                for block in message:
                    # 逐行处理，避免一次性加载大量数据
                    for row in block:
                        # 立即处理数据，不存储
                        process_row_immediately(row)
                        processed_count += 1

                        # 定期执行垃圾回收
                        if processed_count % gc_interval == 0:
                            gc.collect()
                            print(f"已处理 {processed_count} 条记录，执行垃圾回收")

                consumer.commit(message)
            else:
                break

    finally:
        consumer.unsubscribe()
        consumer.close()
        print(f"总共处理 {processed_count} 条记录")

def process_row_immediately(row):
    """立即处理行数据"""
    # 实现即时处理逻辑，避免数据积累
    pass
```

### 3. 监控和指标收集
```python
import time
from collections import defaultdict
from datetime import datetime

class ConsumerMetrics:
    """消费者指标收集器"""
    def __init__(self):
        self.start_time = time.time()
        self.message_count = 0
        self.row_count = 0
        self.error_count = 0
        self.topic_stats = defaultdict(int)
        self.processing_times = []

    def record_message(self, message, processing_time):
        """记录消息处理指标"""
        self.message_count += 1
        self.topic_stats[message.topic()] += 1
        self.processing_times.append(processing_time)

        # 只保留最近1000次的处理时间
        if len(self.processing_times) > 1000:
            self.processing_times = self.processing_times[-1000:]

    def record_rows(self, count):
        """记录行数"""
        self.row_count += count

    def record_error(self):
        """记录错误"""
        self.error_count += 1

    def get_stats(self):
        """获取统计信息"""
        elapsed_time = time.time() - self.start_time
        avg_processing_time = (sum(self.processing_times) / len(self.processing_times)
                              if self.processing_times else 0)

        return {
            'elapsed_time': elapsed_time,
            'message_count': self.message_count,
            'row_count': self.row_count,
            'error_count': self.error_count,
            'messages_per_second': self.message_count / elapsed_time if elapsed_time > 0 else 0,
            'rows_per_second': self.row_count / elapsed_time if elapsed_time > 0 else 0,
            'avg_processing_time': avg_processing_time,
            'topic_stats': dict(self.topic_stats)
        }

def monitored_consumer():
    """带监控的消费者"""
    conf = {
        "td.connect.websocket.scheme": "ws",
        "group.id": "monitored_group",
        "auto.offset.reset": "earliest",
    }

    consumer = taosws.Consumer(conf)
    consumer.subscribe(["monitored_topic"])

    metrics = ConsumerMetrics()

    try:
        while True:
            message = consumer.poll(timeout=5.0)
            if message:
                start_time = time.time()

                try:
                    row_count = 0
                    for block in message:
                        row_count += block.nrows()
                        for row in block:
                            process_row(row)

                    processing_time = time.time() - start_time
                    metrics.record_message(message, processing_time)
                    metrics.record_rows(row_count)

                    consumer.commit(message)

                except Exception as e:
                    metrics.record_error()
                    print(f"处理错误: {e}")
                    consumer.commit(message)  # 跳过错误消息

                # 定期输出统计信息
                if metrics.message_count % 100 == 0:
                    stats = metrics.get_stats()
                    print(f"统计信息: {stats}")
            else:
                break

    finally:
        # 输出最终统计信息
        final_stats = metrics.get_stats()
        print(f"最终统计: {final_stats}")

        consumer.unsubscribe()
        consumer.close()
```

## 完整实战示例

### IoT 数据实时处理系统
```python
import taosws
import json
import time
import threading
from datetime import datetime
from collections import defaultdict

class IoTDataProcessor:
    """IoT数据实时处理系统"""

    def __init__(self, config):
        self.config = config
        self.consumer = None
        self.running = False
        self.stats = defaultdict(int)
        self.alert_thresholds = {
            'temperature': {'min': -10, 'max': 50},
            'humidity': {'min': 0, 'max': 100},
            'pressure': {'min': 900, 'max': 1100}
        }

    def start(self):
        """启动处理系统"""
        try:
            # 创建消费者
            self.consumer = taosws.Consumer(self.config)
            self.consumer.subscribe(["iot_sensor_data", "iot_device_status"])

            self.running = True
            print("IoT数据处理系统已启动")

            # 启动监控线程
            monitor_thread = threading.Thread(target=self._monitor_stats)
            monitor_thread.daemon = True
            monitor_thread.start()

            # 主处理循环
            self._process_loop()

        except Exception as e:
            print(f"系统启动失败: {e}")
        finally:
            self.stop()

    def _process_loop(self):
        """主处理循环"""
        consecutive_empty = 0
        max_empty = 20

        while self.running and consecutive_empty < max_empty:
            try:
                message = self.consumer.poll(timeout=3.0)
                if message:
                    consecutive_empty = 0
                    self._process_message(message)
                    self.consumer.commit(message)
                else:
                    consecutive_empty += 1

            except Exception as e:
                print(f"处理循环错误: {e}")
                self.stats['errors'] += 1
                time.sleep(1)

    def _process_message(self, message):
        """处理单条消息"""
        topic = message.topic()
        database = message.database()

        print(f"处理消息: Topic={topic}, DB={database}")

        for block in message:
            if topic == "iot_sensor_data":
                self._process_sensor_data(block)
            elif topic == "iot_device_status":
                self._process_device_status(block)

        self.stats['messages_processed'] += 1

    def _process_sensor_data(self, block):
        """处理传感器数据"""
        for row in block:
            try:
                # 假设数据格式: [timestamp, device_id, sensor_type, value, location]
                timestamp, device_id, sensor_type, value, location = row

                # 数据验证
                if self._validate_sensor_data(sensor_type, value):
                    # 存储到时序数据库
                    self._store_sensor_data(timestamp, device_id, sensor_type, value, location)

                    # 检查告警条件
                    if self._check_alert_condition(sensor_type, value):
                        self._send_alert(device_id, sensor_type, value, location)

                    self.stats['sensor_data_processed'] += 1
                else:
                    print(f"无效传感器数据: {row}")
                    self.stats['invalid_data'] += 1

            except Exception as e:
                print(f"处理传感器数据错误: {e}")
                self.stats['processing_errors'] += 1

    def _process_device_status(self, block):
        """处理设备状态数据"""
        for row in block:
            try:
                # 假设数据格式: [timestamp, device_id, status, battery_level, signal_strength]
                timestamp, device_id, status, battery_level, signal_strength = row

                # 更新设备状态
                self._update_device_status(device_id, status, battery_level, signal_strength)

                # 检查设备健康状态
                if battery_level < 20:
                    self._send_device_alert(device_id, "低电量", battery_level)

                if signal_strength < -80:
                    self._send_device_alert(device_id, "信号弱", signal_strength)

                self.stats['device_status_processed'] += 1

            except Exception as e:
                print(f"处理设备状态错误: {e}")
                self.stats['processing_errors'] += 1

    def _validate_sensor_data(self, sensor_type, value):
        """验证传感器数据"""
        if sensor_type in self.alert_thresholds:
            thresholds = self.alert_thresholds[sensor_type]
            return thresholds['min'] <= value <= thresholds['max']
        return True

    def _check_alert_condition(self, sensor_type, value):
        """检查告警条件"""
        if sensor_type in self.alert_thresholds:
            thresholds = self.alert_thresholds[sensor_type]
            return value < thresholds['min'] or value > thresholds['max']
        return False

    def _store_sensor_data(self, timestamp, device_id, sensor_type, value, location):
        """存储传感器数据"""
        # 实现数据存储逻辑
        pass

    def _update_device_status(self, device_id, status, battery_level, signal_strength):
        """更新设备状态"""
        # 实现设备状态更新逻辑
        pass

    def _send_alert(self, device_id, sensor_type, value, location):
        """发送传感器告警"""
        alert_msg = {
            'type': 'sensor_alert',
            'device_id': device_id,
            'sensor_type': sensor_type,
            'value': value,
            'location': location,
            'timestamp': datetime.now().isoformat()
        }
        print(f"传感器告警: {alert_msg}")
        self.stats['alerts_sent'] += 1

    def _send_device_alert(self, device_id, alert_type, value):
        """发送设备告警"""
        alert_msg = {
            'type': 'device_alert',
            'device_id': device_id,
            'alert_type': alert_type,
            'value': value,
            'timestamp': datetime.now().isoformat()
        }
        print(f"设备告警: {alert_msg}")
        self.stats['device_alerts_sent'] += 1

    def _monitor_stats(self):
        """监控统计信息"""
        while self.running:
            time.sleep(30)  # 每30秒输出一次统计
            print(f"系统统计: {dict(self.stats)}")

    def stop(self):
        """停止处理系统"""
        self.running = False
        if self.consumer:
            self.consumer.unsubscribe()
            self.consumer.close()
        print("IoT数据处理系统已停止")

# 使用示例
def main():
    config = {
        "td.connect.websocket.scheme": "ws",
        "td.connect.ip": "localhost",
        "td.connect.port": "6041",
        "group.id": "iot_processor_group",
        "client.id": "iot_processor_001",
        "auto.offset.reset": "latest",
        "enable.auto.commit": "false",
        "fetch.max.wait.ms": "2000",
        "min.poll.rows": "500",
    }

    processor = IoTDataProcessor(config)

    try:
        processor.start()
    except KeyboardInterrupt:
        print("收到停止信号")
    finally:
        processor.stop()

if __name__ == "__main__":
    main()
```

## 常见问题和解决方案

### 1. 消费延迟问题
**问题**: 消费者处理速度跟不上数据产生速度
**解决方案**:
- 增加消费者数量（同一消费者组）
- 优化数据处理逻辑
- 调整 `fetch.max.wait.ms` 和 `min.poll.rows` 参数
- 使用批量处理

### 2. 重复消费问题
**问题**: 消息被重复处理
**解决方案**:
- 确保正确提交偏移量
- 实现幂等性处理逻辑
- 使用手动提交模式

### 3. 消费者组重平衡
**问题**: 消费者加入或离开时的重平衡影响性能
**解决方案**:
- 合理设置消费者数量
- 避免频繁的消费者启停
- 监控重平衡事件

### 4. 内存使用过高
**问题**: 长时间运行导致内存泄漏
**解决方案**:
- 及时释放消息对象
- 定期执行垃圾回收
- 限制缓存大小

## 总结

TDengine 的订阅功能提供了强大的流式数据处理能力，主要优势包括：

1. **高性能**: 基于 WebSocket 的高效通信
2. **可扩展**: 支持消费者组和负载均衡
3. **可靠性**: 支持偏移量管理和故障恢复
4. **灵活性**: 支持多种订阅模式和过滤条件

**最佳实践总结**:
- 合理配置消费者参数
- 实现健壮的错误处理机制
- 监控消费性能和系统指标
- 根据业务需求选择合适的提交策略
- 定期维护和优化消费者代码

通过合理使用订阅功能，可以构建高效、可靠的实时数据处理系统，满足各种 IoT、监控、分析等场景的需求。
```
